// components/Footer.jsx
'use client';
import React from 'react';
import { motion } from 'framer-motion';
import AnimatedLink from '@/components/AnimatedLink';
import styles from './style.module.scss';
import { useTranslation } from '@/hooks/useTranslation';
import LanguageSwitcher from '@/components/LanguageSwitcher';

export default function Footer({ locale = 'fr' }) {
  const { t } = useTranslation('footer');

  const containerVariants = {
    hidden: {},
    show: {
      transition: {
        delayChildren: 0.1,    // 100 ms avant le 1er
        staggerChildren: 0.10, //  80 ms entre chaque
      }
    }
  };
  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    show:  {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,        // 300 ms au lieu de 600
        ease: 'easeOut'
      }
    }
  };
  

  return (
    <div className={styles.footerContainer}>
      <div className={`${styles.footerContent} container`}>
        {/* Logo simple sans animation */}
        <div className={styles.footerLogo}>
          Kapreon
        </div>

        {/* on met tout le reste dans ce motion.div */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, amount: 0.1 }}
        >
              <div className={styles.footerSection}>
                <div className={styles.footerBlocks}>
                  <div className={`${styles.infoBlock} ${styles.footerBlock}`}>
                    <motion.p variants={itemVariants} className={styles.infoBlockTitle}>
                      {t('montreal')}
                    </motion.p>
                    <motion.p variants={itemVariants} dangerouslySetInnerHTML={{ __html: t('address_montreal') }}>
                    </motion.p>
                    <motion.div variants={itemVariants}>
                      <AnimatedLink href="tel:+14388033053">
                        {t('phone_montreal')}
                      </AnimatedLink>
                    </motion.div>
                  </div>

                  <div className={styles.footerBlock}>
                    <motion.p variants={itemVariants} className={styles.infoBlockTitle}>
                      {t('paris')}
                    </motion.p>
                    <motion.div variants={itemVariants}>
                      <AnimatedLink href="tel:+33285528323">
                        {t('phone_paris')}
                      </AnimatedLink>
                    </motion.div>
                  </div>
                </div>

                <div className={styles.footerBlocks}>
                  <ul className={styles.footerBlock}>
                    <li>
                      <motion.div variants={itemVariants}>
                        <AnimatedLink href={`/${locale}`} standalone>
                          {t('case_studies')}
                        </AnimatedLink>
                      </motion.div>
                    </li>
                    <li>
                      <motion.div variants={itemVariants}>
                        <AnimatedLink href={`/${locale}/questions-frequentes`} standalone>
                          {t('faq')}
                        </AnimatedLink>
                      </motion.div>
                    </li>
                    <li>
                      <motion.div variants={itemVariants}>
                        <AnimatedLink href={`/${locale}/contact`} standalone>
                          {t('contact_agency')}
                        </AnimatedLink>
                      </motion.div>
                    </li>
                                        <li>
                      <motion.div variants={itemVariants}>
                        <AnimatedLink href={`/${locale}/blog`} standalone>
                          {t('blog')}
                        </AnimatedLink>
                      </motion.div>
                    </li>
                  </ul>
                  <ul className={styles.footerBlock}>
                    <li>
                      <motion.div variants={itemVariants}>
                        <AnimatedLink href="https://www.linkedin.com/company/kapreon" standalone>
                          {t('linkedin')}
                        </AnimatedLink>
                      </motion.div>
                    </li>
                    <li>
                      <motion.div variants={itemVariants}>
                        <AnimatedLink href="https://www.instagram.com/kapreon_" standalone>
                          {t('instagram')}
                        </AnimatedLink>
                      </motion.div>
                    </li>
                    <li>
                      <motion.div variants={itemVariants}>
                        <AnimatedLink href="https://www.behance.net/kapreon" standalone>
                          {t('behance')}
                        </AnimatedLink>
                      </motion.div>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Language Switcher */}
              <motion.div variants={itemVariants} className={styles.footerBlock}>
                <LanguageSwitcher locale={locale} />
              </motion.div>

              <div className={`${styles.footerLegal} ${styles.footerBlock}`}>
                <motion.small variants={itemVariants}>
                  {t('copyright', { year: new Date().getFullYear() })}
                </motion.small>
                <div className={styles.footerLegalLinks}>
                  <motion.small variants={itemVariants}>
                    <AnimatedLink href={`/${locale}/legal/termes-conditions`} standalone>
                      {t('terms_conditions')}
                    </AnimatedLink>
                  </motion.small>
                  <motion.small variants={itemVariants}>
                    <AnimatedLink href={`/${locale}/legal/cookies`} standalone>
                      {t('cookie_usage')}
                    </AnimatedLink>
                  </motion.small>
                </div>
              </div>
        </motion.div>
      </div>
    </div>
  );
}
